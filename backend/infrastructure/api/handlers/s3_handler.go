package handlers

import (
	"bufio"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"syscall"

	"cbi-e2e-analytics/infrastructure/aws"
	"cbi-e2e-analytics/infrastructure/scheduler"

	"github.com/gin-gonic/gin"
)

type S3Handler struct {
	s3Service *aws.S3Service
	dataSource *aws.S3DataSource
	scheduler  *scheduler.S3Scheduler
}

func NewS3Handler(s3Service *aws.S3Service, dataSource *aws.S3DataSource, scheduler *scheduler.S3Scheduler) *S3Handler {
	return &S3Handler{
		s3Service:  s3Service,
		dataSource: dataSource,
		scheduler:  scheduler,
	}
}

func (h *S3Handler) GetS3Status(c *gin.Context) {
	if h.s3Service == nil {
		c.JSON(http.StatusOK, gin.H{
			"s3Enabled":        false,
			"message":          "S3 integration not configured",
			"schedulerRunning": false,
			"processedFiles":   0,
		})
		return
	}

	c.JSO<PERSON>(http.StatusOK, gin.H{
		"s3Enabled":        true,
		"bucket":           h.s3Service.GetBucketName(),
		"schedulerRunning": h.scheduler != nil && h.scheduler.IsRunning(),
		"processedFiles":   h.getProcessedFileCount(),
		"maxFiles":         h.getMaxFiles(),
	})
}

func (h *S3Handler) TriggerSync(c *gin.Context) {
	if h.dataSource == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "S3 data source not configured",
		})
		return
	}

	// Clear cache before sync to ensure we get the latest runs
	h.dataSource.ClearProcessedCache()

	go func() {
		if err := h.dataSource.ProcessS3FilesManual(); err != nil {
			log.Printf("Manual S3 sync error: %v", err)
		}
	}()

	c.JSON(http.StatusOK, gin.H{
		"message": "Manual S3 sync triggered (processing nightly runs from last 5 days)",
	})
}

func (h *S3Handler) ClearCache(c *gin.Context) {
	if h.dataSource == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "S3 data source not configured",
		})
		return
	}

	h.dataSource.ClearProcessedCache()

	c.JSON(http.StatusOK, gin.H{
		"message": "Processed files cache cleared",
	})
}

func (h *S3Handler) getProcessedFileCount() int {
	if h.dataSource == nil {
		return 0
	}
	return h.dataSource.GetProcessedFileCount()
}

func (h *S3Handler) getMaxFiles() int {
	if h.dataSource == nil {
		return 0
	}
	return h.dataSource.GetMaxFiles()
}

// GetMaxFiles returns the current max files configuration
func (h *S3Handler) GetMaxFiles(c *gin.Context) {
	if h.dataSource == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "S3 data source not configured",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"maxFiles": h.dataSource.GetMaxFiles(),
	})
}

// SetMaxFiles updates the max files configuration
func (h *S3Handler) SetMaxFiles(c *gin.Context) {
	if h.dataSource == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "S3 data source not configured",
		})
		return
	}

	maxFilesStr := c.Param("maxFiles")
	maxFiles, err := strconv.Atoi(maxFilesStr)
	if err != nil || maxFiles < 1 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid maxFiles value. Must be a positive integer.",
		})
		return
	}

	h.dataSource.SetMaxFiles(maxFiles)

	c.JSON(http.StatusOK, gin.H{
		"message":  "Max files updated successfully",
		"maxFiles": maxFiles,
	})
}

// S3ConfigRequest represents the request body for updating S3 configuration
type S3ConfigRequest struct {
	Enabled         bool   `json:"enabled"`
	Bucket          string `json:"bucket"`
	Region          string `json:"region"`
	AccessKeyID     string `json:"accessKeyId"`
	SecretAccessKey string `json:"secretAccessKey"`
	SessionToken    string `json:"sessionToken"`
	DataPrefix      string `json:"dataPrefix"`
	VideoPrefix     string `json:"videoPrefix"`
	SyncInterval    int    `json:"syncInterval"`
	SyncDays        int    `json:"syncDays"`
	MaxFiles        int    `json:"maxFiles"`
}

// GetS3Config returns the current S3 configuration
func (h *S3Handler) GetS3Config(c *gin.Context) {
	config, err := h.readEnvConfig()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to read configuration: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, config)
}

// UpdateS3Config updates the S3 configuration and optionally restarts the backend
func (h *S3Handler) UpdateS3Config(c *gin.Context) {
	var req S3ConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": fmt.Sprintf("Invalid request body: %v", err),
		})
		return
	}

	// Update the .env file
	if err := h.updateEnvFile(req); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to update configuration: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "S3 configuration updated successfully. Restart the backend to apply changes.",
		"config":  req,
	})
}

// RestartBackend restarts the backend application
func (h *S3Handler) RestartBackend(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"message": "Backend restart initiated. The application will restart in a few seconds.",
	})

	// Give the response time to be sent before restarting
	go func() {
		log.Println("Restarting backend application...")

		// Get the current process
		pid := os.Getpid()
		process, err := os.FindProcess(pid)
		if err != nil {
			log.Printf("Failed to find current process: %v", err)
			return
		}

		// Send SIGTERM to gracefully shutdown
		if err := process.Signal(syscall.SIGTERM); err != nil {
			log.Printf("Failed to send SIGTERM: %v", err)
			// Fallback to SIGKILL
			process.Signal(syscall.SIGKILL)
		}
	}()
}

// readEnvConfig reads the current configuration from the .env file
func (h *S3Handler) readEnvConfig() (*S3ConfigRequest, error) {
	envPath := filepath.Join(".", ".env")

	config := &S3ConfigRequest{
		Enabled:      false,
		Region:       "eu-central-1",
		SyncInterval: 5,
		SyncDays:     3,
		MaxFiles:     10,
	}

	file, err := os.Open(envPath)
	if err != nil {
		return config, err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) != 2 {
			continue
		}

		key := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])

		switch key {
		case "ENABLE_S3_INTEGRATION":
			config.Enabled = value == "true"
		case "AWS_S3_BUCKET":
			config.Bucket = value
		case "AWS_REGION":
			config.Region = value
		case "AWS_ACCESS_KEY_ID":
			config.AccessKeyID = value
		case "AWS_SECRET_ACCESS_KEY":
			config.SecretAccessKey = value
		case "AWS_SESSION_TOKEN":
			config.SessionToken = value
		case "AWS_S3_DATA_PREFIX":
			config.DataPrefix = value
		case "AWS_S3_VIDEO_PREFIX":
			config.VideoPrefix = value
		case "S3_SYNC_INTERVAL_MINUTES":
			if val, err := strconv.Atoi(value); err == nil {
				config.SyncInterval = val
			}
		case "S3_SYNC_DAYS":
			if val, err := strconv.Atoi(value); err == nil {
				config.SyncDays = val
			}
		}
	}

	return config, scanner.Err()
}

// updateEnvFile updates the .env file with new configuration
func (h *S3Handler) updateEnvFile(config S3ConfigRequest) error {
	envPath := filepath.Join(".", ".env")

	// Read existing file
	var lines []string
	file, err := os.Open(envPath)
	if err != nil {
		// If file doesn't exist, create it
		lines = []string{}
	} else {
		scanner := bufio.NewScanner(file)
		for scanner.Scan() {
			lines = append(lines, scanner.Text())
		}
		file.Close()
		if err := scanner.Err(); err != nil {
			return err
		}
	}

	// Update or add configuration values
	updates := map[string]string{
		"ENABLE_S3_INTEGRATION":     fmt.Sprintf("%t", config.Enabled),
		"AWS_S3_BUCKET":             config.Bucket,
		"AWS_REGION":                config.Region,
		"AWS_ACCESS_KEY_ID":         config.AccessKeyID,
		"AWS_SECRET_ACCESS_KEY":     config.SecretAccessKey,
		"AWS_SESSION_TOKEN":         config.SessionToken,
		"AWS_S3_DATA_PREFIX":        config.DataPrefix,
		"AWS_S3_VIDEO_PREFIX":       config.VideoPrefix,
		"S3_SYNC_INTERVAL_MINUTES":  fmt.Sprintf("%d", config.SyncInterval),
		"S3_SYNC_DAYS":              fmt.Sprintf("%d", config.SyncDays),
	}

	// Update existing lines or mark for addition
	updatedKeys := make(map[string]bool)
	for i, line := range lines {
		trimmed := strings.TrimSpace(line)
		if trimmed == "" || strings.HasPrefix(trimmed, "#") {
			continue
		}

		parts := strings.SplitN(trimmed, "=", 2)
		if len(parts) != 2 {
			continue
		}

		key := strings.TrimSpace(parts[0])
		if newValue, exists := updates[key]; exists {
			lines[i] = fmt.Sprintf("%s=%s", key, newValue)
			updatedKeys[key] = true
		}
	}

	// Add new keys that weren't found
	for key, value := range updates {
		if !updatedKeys[key] {
			lines = append(lines, fmt.Sprintf("%s=%s", key, value))
		}
	}

	// Write back to file
	return os.WriteFile(envPath, []byte(strings.Join(lines, "\n")+"\n"), 0644)
}
