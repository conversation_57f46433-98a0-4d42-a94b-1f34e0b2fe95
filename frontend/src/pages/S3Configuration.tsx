import React, { useState, useEffect } from "react";
import {
  Card,
  Form,
  Input,
  Button,
  Switch,
  Al<PERSON>,
  Divider,
  Statistic,
} from "antd";
import {
  CloudOutlined,
  SyncOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
} from "@ant-design/icons";
import { motion } from "framer-motion";
import toast from "react-hot-toast";
import apiService from "@/services/api";
import { S3Status, S3Config } from "@/types";

const S3Configuration: React.FC = () => {
  const [form] = Form.useForm();
  const [status, setStatus] = useState<S3Status | null>(null);
  const [statusLoading, setStatusLoading] = useState(true);
  const [configLoading, setConfigLoading] = useState(false);
  const [updateLoading, setUpdateLoading] = useState(false);

  useEffect(() => {
    loadS3Status();
    loadS3Config();
  }, []);

  const loadS3Status = async () => {
    try {
      setStatusLoading(true);
      const data = await apiService.getS3Status();
      setStatus(data);
    } catch (error) {
      console.error("Failed to load S3 status:", error);
      toast.error("Failed to load S3 status");
    } finally {
      setStatusLoading(false);
    }
  };

  const loadS3Config = async () => {
    try {
      setConfigLoading(true);
      const config = await apiService.getS3Config();
      form.setFieldsValue({
        enabled: config.enabled,
        bucket: config.bucket,
        region: config.region,
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey,
        sessionToken: config.sessionToken,
        dataPrefix: config.dataPrefix,
        videoPrefix: config.videoPrefix,
        syncInterval: config.syncInterval,
        syncDays: config.syncDays,
        maxFiles: config.maxFiles,
      });
    } catch (error) {
      console.error("Failed to load S3 config:", error);
      toast.error("Failed to load S3 configuration");
    } finally {
      setConfigLoading(false);
    }
  };

  const handleTriggerSync = async () => {
    try {
      await apiService.triggerS3Sync();
      // toast.success("S3 sync triggered successfully");
      // Reload status after a short delay
      setTimeout(loadS3Status, 2000);
    } catch (error) {
      console.error("Failed to trigger S3 sync:", error);
      toast.error("Failed to trigger S3 sync");
    }
  };

  const handleClearCache = async () => {
    try {
      await apiService.clearS3Cache();
      toast.success("S3 cache cleared successfully");
      loadS3Status();
    } catch (error) {
      console.error("Failed to clear S3 cache:", error);
      toast.error("Failed to clear S3 cache");
    }
  };

  const handleUpdateConfig = async () => {
    try {
      setUpdateLoading(true);
      const values = await form.validateFields();
      await apiService.updateS3Config(values);
      toast.success("Configuration updated successfully!");
      loadS3Status();
      loadS3Config();
    } catch (error) {
      console.error("Failed to update S3 config:", error);
      toast.error("Failed to update S3 configuration");
    } finally {
      setUpdateLoading(false);
    }
  };

  const handleRestartBackend = async () => {
    try {
      await apiService.restartBackend();
      toast.success("Backend restart initiated. Please wait a moment...");
      // Reload the page after a delay to reconnect
      setTimeout(() => {
        window.location.reload();
      }, 3000);
    } catch (error) {
      console.error("Failed to restart backend:", error);
      toast.error("Failed to restart backend");
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="p-6 space-y-6"
    >
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">S3 Integration</h1>
          <p className="text-gray-600 mt-1">
            Configure S3 integration for video storage and data source
          </p>
        </div>
      </div>

      {/* S3 Status Card */}
      <Card
        title={
          <div className="flex items-center space-x-2">
            <CloudOutlined />
            <span>S3 Status</span>
          </div>
        }
        loading={statusLoading}
      >
        {status && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Statistic
                title="Connection Status"
                value={status.s3Enabled ? "Connected" : "Disabled"}
                prefix={
                  status.s3Enabled ? (
                    <CheckCircleOutlined className="text-green-500" />
                  ) : (
                    <CloseCircleOutlined className="text-red-500" />
                  )
                }
                valueStyle={{
                  color: status.s3Enabled ? "#52c41a" : "#ff4d4f",
                }}
              />

              {status.bucket && (
                <Statistic
                  title="S3 Bucket"
                  value={status.bucket}
                  prefix={<CloudOutlined />}
                />
              )}

              {status.schedulerRunning !== undefined && (
                <Statistic
                  title="Scheduler"
                  value={status.schedulerRunning ? "Running" : "Stopped"}
                  prefix={<SyncOutlined />}
                  valueStyle={{
                    color: status.schedulerRunning ? "#52c41a" : "#ff4d4f",
                  }}
                />
              )}

              {status.processedFiles !== undefined && (
                <Statistic
                  title="Processed Files"
                  value={status.processedFiles}
                  prefix={<CloudOutlined />}
                />
              )}
            </div>

            {!status.s3Enabled && (
              <Alert
                message="S3 Integration Disabled"
                description={
                  status.message ||
                  "S3 integration is not enabled. Configure environment variables to enable S3 features."
                }
                type="warning"
                showIcon
              />
            )}

            {status.s3Enabled && (
              <div className="flex items-center space-x-4">
                <Button
                  type="primary"
                  icon={<SyncOutlined />}
                  onClick={handleTriggerSync}
                >
                  Trigger Sync
                </Button>
                <Button icon={<DeleteOutlined />} onClick={handleClearCache}>
                  Clear Cache
                </Button>
                <Button onClick={loadS3Status}>Refresh Status</Button>
              </div>
            )}

            <div className="flex items-center space-x-4 mt-4">
              <Button
                type="primary"
                icon={<SyncOutlined />}
                onClick={handleUpdateConfig}
                loading={updateLoading}
              >
                Update Configuration
              </Button>
              <Button type="default" danger onClick={handleRestartBackend}>
                Restart Backend
              </Button>
            </div>
          </div>
        )}
      </Card>

      {/* Configuration Form */}
      <Card
        title={
          <div className="flex items-center space-x-2">
            <CloudOutlined />
            <span>S3 Configuration</span>
          </div>
        }
      >
        <Alert
          message="Environment Configuration Required"
          description="S3 integration is configured through environment variables. Update your .env file with the following variables and restart the application."
          type="info"
          showIcon
          className="mb-6"
        />

        <Form form={form} layout="vertical">
          <Form.Item
            name="enabled"
            valuePropName="checked"
            label="Enable S3 Integration"
          >
            <Switch checkedChildren="Enabled" unCheckedChildren="Disabled" />
          </Form.Item>

          <Divider />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Form.Item
              name="bucket"
              label="S3 Bucket Name"
              rules={[
                { required: true, message: "Please enter S3 bucket name" },
              ]}
            >
              <Input
                placeholder="my-test-results-bucket"
                addonBefore="AWS_S3_BUCKET="
              />
            </Form.Item>

            <Form.Item
              name="region"
              label="AWS Region"
              rules={[{ required: true, message: "Please enter AWS region" }]}
            >
              <Input placeholder="eu-central-1" addonBefore="AWS_REGION=" />
            </Form.Item>
          </div>

          <div className="grid grid-cols-1 gap-4">
            <Form.Item name="accessKeyId" label="AWS Access Key ID">
              <Input.Password
                placeholder="Your AWS Access Key ID"
                addonBefore="AWS_ACCESS_KEY_ID="
              />
            </Form.Item>

            <Form.Item name="secretAccessKey" label="AWS Secret Access Key">
              <Input.Password
                placeholder="Your AWS Secret Access Key"
                addonBefore="AWS_SECRET_ACCESS_KEY="
              />
            </Form.Item>

            <Form.Item name="sessionToken" label="AWS Session Token (Optional)">
              <Input.Password
                placeholder="Your AWS Session Token (for temporary credentials)"
                addonBefore="AWS_SESSION_TOKEN="
              />
            </Form.Item>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Form.Item
              name="videoPrefix"
              label="Video Prefix"
              rules={[{ required: true, message: "Please enter video prefix" }]}
            >
              <Input
                placeholder="workflowtest/dev/ourl-lemon/"
                addonBefore="AWS_S3_VIDEO_PREFIX="
              />
            </Form.Item>

            <Form.Item
              name="dataPrefix"
              label="Data Prefix"
              rules={[{ required: true, message: "Please enter data prefix" }]}
            >
              <Input
                placeholder="workflowtest/dev/ourl-lemon/"
                addonBefore="AWS_S3_DATA_PREFIX="
              />
            </Form.Item>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Form.Item
              name="syncInterval"
              label="Sync Interval (minutes)"
              rules={[
                { required: true, message: "Please enter sync interval" },
              ]}
            >
              <Input
                type="number"
                min={1}
                max={60}
                placeholder="5"
                addonBefore="S3_SYNC_INTERVAL_MINUTES="
              />
            </Form.Item>

            <Form.Item
              name="syncDays"
              label="Sync Days"
              rules={[{ required: true, message: "Please enter sync days" }]}
            >
              <Input
                type="number"
                min={1}
                max={30}
                placeholder="3"
                addonBefore="S3_SYNC_DAYS="
              />
            </Form.Item>

            <Form.Item
              name="maxFiles"
              label="Max Files"
              rules={[{ required: true, message: "Please enter max files" }]}
            >
              <Input
                type="number"
                min={1}
                max={100}
                placeholder="10"
                addonBefore="S3_MAX_FILES="
              />
            </Form.Item>
          </div>

          <Alert
            message="Configuration Management"
            description={
              <div className="space-y-2">
                <p>
                  You can now configure S3 integration directly from this UI.
                  Fill in the form above and click "Update Configuration" to
                  save changes.
                </p>
                <p className="text-sm text-gray-600">
                  <strong>Important:</strong> After updating the configuration,
                  click "Restart Backend" to apply the changes. The backend will
                  restart automatically and reload with the new settings.
                </p>
                <p className="text-sm text-gray-600">
                  <strong>Security Note:</strong> Credentials are stored in the
                  backend .env file. Make sure your server environment is
                  secure.
                </p>
              </div>
            }
            type="info"
            className="mt-4"
          />
        </Form>
      </Card>
    </motion.div>
  );
};

export default S3Configuration;
