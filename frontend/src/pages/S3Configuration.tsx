import React, { useState, useEffect } from "react";
import {
  Card,
  Form,
  Input,
  But<PERSON>,
  Switch,
  Al<PERSON>,
  Divider,
  Statistic,
} from "antd";
import {
  CloudOutlined,
  SyncOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
} from "@ant-design/icons";
import { motion } from "framer-motion";
import toast from "react-hot-toast";
import apiService from "@/services/api";
import { S3Status, S3Config } from "@/types";

const S3Configuration: React.FC = () => {
  const [form] = Form.useForm();
  const [status, setStatus] = useState<S3Status | null>(null);
  const [statusLoading, setStatusLoading] = useState(true);

  useEffect(() => {
    loadS3Status();
  }, []);

  const loadS3Status = async () => {
    try {
      setStatusLoading(true);
      const data = await apiService.getS3Status();
      setStatus(data);
      if (data) {
        form.setFieldsValue({
          enabled: data.s3Enabled,
          bucket: data.bucket,
        });
      }
    } catch (error) {
      console.error("Failed to load S3 status:", error);
      toast.error("Failed to load S3 status");
    } finally {
      setStatusLoading(false);
    }
  };

  const handleTriggerSync = async () => {
    try {
      await apiService.triggerS3Sync();
      // toast.success("S3 sync triggered successfully");
      // Reload status after a short delay
      setTimeout(loadS3Status, 2000);
    } catch (error) {
      console.error("Failed to trigger S3 sync:", error);
      toast.error("Failed to trigger S3 sync");
    }
  };

  const handleClearCache = async () => {
    try {
      await apiService.clearS3Cache();
      toast.success("S3 cache cleared successfully");
      loadS3Status();
    } catch (error) {
      console.error("Failed to clear S3 cache:", error);
      toast.error("Failed to clear S3 cache");
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="p-6 space-y-6"
    >
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">S3 Integration</h1>
          <p className="text-gray-600 mt-1">
            Configure S3 integration for video storage and data source
          </p>
        </div>
      </div>

      {/* S3 Status Card */}
      <Card
        title={
          <div className="flex items-center space-x-2">
            <CloudOutlined />
            <span>S3 Status</span>
          </div>
        }
        loading={statusLoading}
      >
        {status && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Statistic
                title="Connection Status"
                value={status.s3Enabled ? "Connected" : "Disabled"}
                prefix={
                  status.s3Enabled ? (
                    <CheckCircleOutlined className="text-green-500" />
                  ) : (
                    <CloseCircleOutlined className="text-red-500" />
                  )
                }
                valueStyle={{
                  color: status.s3Enabled ? "#52c41a" : "#ff4d4f",
                }}
              />

              {status.bucket && (
                <Statistic
                  title="S3 Bucket"
                  value={status.bucket}
                  prefix={<CloudOutlined />}
                />
              )}

              {status.schedulerRunning !== undefined && (
                <Statistic
                  title="Scheduler"
                  value={status.schedulerRunning ? "Running" : "Stopped"}
                  prefix={<SyncOutlined />}
                  valueStyle={{
                    color: status.schedulerRunning ? "#52c41a" : "#ff4d4f",
                  }}
                />
              )}

              {status.processedFiles !== undefined && (
                <Statistic
                  title="Processed Files"
                  value={status.processedFiles}
                  prefix={<CloudOutlined />}
                />
              )}
            </div>

            {!status.s3Enabled && (
              <Alert
                message="S3 Integration Disabled"
                description={
                  status.message ||
                  "S3 integration is not enabled. Configure environment variables to enable S3 features."
                }
                type="warning"
                showIcon
              />
            )}

            {status.s3Enabled && (
              <div className="flex items-center space-x-4">
                <Button
                  type="primary"
                  icon={<SyncOutlined />}
                  onClick={handleTriggerSync}
                >
                  Trigger Sync
                </Button>
                <Button icon={<DeleteOutlined />} onClick={handleClearCache}>
                  Clear Cache
                </Button>
                <Button onClick={loadS3Status}>Refresh Status</Button>
              </div>
            )}
          </div>
        )}
      </Card>

      {/* Configuration Form */}
      <Card
        title={
          <div className="flex items-center space-x-2">
            <CloudOutlined />
            <span>S3 Configuration</span>
          </div>
        }
      >
        <Alert
          message="Environment Configuration Required"
          description="S3 integration is configured through environment variables. Update your .env file with the following variables and restart the application."
          type="info"
          showIcon
          className="mb-6"
        />

        <Form form={form} layout="vertical">
          <Form.Item
            name="enabled"
            valuePropName="checked"
            label="Enable S3 Integration"
          >
            <Switch
              checkedChildren="Enabled"
              unCheckedChildren="Disabled"
              disabled={true}
              title="S3 integration is configured through environment variables"
            />
          </Form.Item>

          <Divider />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Form.Item
              name="bucket"
              label="S3 Bucket Name"
              rules={[
                { required: true, message: "Please enter S3 bucket name" },
              ]}
            >
              <Input
                placeholder="my-test-results-bucket"
                disabled
                addonBefore="AWS_S3_BUCKET="
              />
            </Form.Item>

            <Form.Item
              name="region"
              label="AWS Region"
              initialValue="us-east-1"
            >
              <Input
                placeholder="us-east-1"
                disabled
                addonBefore="AWS_REGION="
              />
            </Form.Item>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Form.Item
              name="videoPrefix"
              label="Video Prefix"
              initialValue="test-videos/"
            >
              <Input
                placeholder="test-videos/"
                disabled
                addonBefore="AWS_S3_VIDEO_PREFIX="
              />
            </Form.Item>

            <Form.Item
              name="dataPrefix"
              label="Data Prefix"
              initialValue="test-results/"
            >
              <Input
                placeholder="test-results/"
                disabled
                addonBefore="AWS_S3_DATA_PREFIX="
              />
            </Form.Item>
          </div>

          <Form.Item
            name="syncInterval"
            label="Sync Interval (minutes)"
            initialValue={5}
          >
            <Input
              type="number"
              min={1}
              max={60}
              disabled
              addonBefore="S3_SYNC_INTERVAL_MINUTES="
            />
          </Form.Item>

          <Alert
            message="Configuration Instructions"
            description={
              <div className="space-y-2">
                <p>
                  Add these environment variables to your backend .env file:
                </p>
                <div className="bg-gray-100 p-3 rounded font-mono text-sm">
                  <div>ENABLE_S3_INTEGRATION=true</div>
                  <div>AWS_REGION=us-east-1</div>
                  <div>AWS_ACCESS_KEY_ID=your_access_key</div>
                  <div>AWS_SECRET_ACCESS_KEY=your_secret_key</div>
                  <div>AWS_S3_BUCKET=your-bucket-name</div>
                  <div>AWS_S3_VIDEO_PREFIX=test-videos/</div>
                  <div>AWS_S3_DATA_PREFIX=test-results/</div>
                  <div>S3_SYNC_INTERVAL_MINUTES=5</div>
                </div>
                <p className="text-sm text-gray-600">
                  After updating the environment variables, restart the backend
                  application to apply changes.
                </p>
              </div>
            }
            type="info"
            className="mt-4"
          />
        </Form>
      </Card>
    </motion.div>
  );
};

export default S3Configuration;
