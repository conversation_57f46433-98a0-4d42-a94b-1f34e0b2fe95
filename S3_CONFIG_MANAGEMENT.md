# 🔧 S3 Configuration Management

This document describes the new S3 configuration management feature that allows you to enable/disable and configure S3 integration directly from the UI.

## 🌟 Features

### ✅ What's New
- **UI-based Configuration**: Configure S3 settings directly from the web interface
- **Real-time Enable/Disable**: Toggle S3 integration on/off without manual file editing
- **Backend Restart**: Restart the backend automatically to apply configuration changes
- **Secure Credential Management**: Store AWS credentials securely in the backend environment
- **Configuration Validation**: Form validation ensures all required fields are properly filled

### 🎯 Key Benefits
- No more manual `.env` file editing
- Instant configuration updates
- User-friendly interface for non-technical users
- Automatic backend restart capability
- Real-time status monitoring

## 🚀 How to Use

### 1. Access S3 Configuration
Navigate to the **S3 Configuration** page in your application.

### 2. Configure S3 Settings
Fill in the following fields:

#### **Basic Settings**
- **Enable S3 Integration**: Toggle to enable/disable S3 features
- **S3 Bucket Name**: Your AWS S3 bucket name (e.g., `appsulate-jenkins-reports`)
- **AWS Region**: AWS region where your bucket is located (e.g., `eu-central-1`)

#### **AWS Credentials**
- **AWS Access Key ID**: Your AWS access key
- **AWS Secret Access Key**: Your AWS secret key
- **AWS Session Token**: Optional session token for temporary credentials

#### **S3 Paths**
- **Video Prefix**: Path prefix for video files (e.g., `workflowtest/dev/ourl-lemon/`)
- **Data Prefix**: Path prefix for test data files (e.g., `workflowtest/dev/ourl-lemon/`)

#### **Sync Settings**
- **Sync Interval**: How often to sync data (in minutes, 1-60)
- **Sync Days**: Number of days to look back for data (1-30)
- **Max Files**: Maximum number of files to process (1-100)

### 3. Update Configuration
1. Fill in all required fields
2. Click **"Update Configuration"** button
3. Wait for the success confirmation

### 4. Restart Backend
1. Click **"Restart Backend"** button
2. Wait for the restart confirmation
3. The page will automatically reload after 3 seconds

## 🔧 Technical Implementation

### Backend Endpoints

#### Get Current Configuration
```http
GET /api/v1/s3/config
```
Returns the current S3 configuration from the `.env` file.

#### Update Configuration
```http
PUT /api/v1/s3/config
Content-Type: application/json

{
  "enabled": true,
  "bucket": "my-bucket",
  "region": "eu-central-1",
  "accessKeyId": "AKIA...",
  "secretAccessKey": "...",
  "sessionToken": "...",
  "dataPrefix": "test-data/",
  "videoPrefix": "test-videos/",
  "syncInterval": 5,
  "syncDays": 3,
  "maxFiles": 10
}
```

#### Restart Backend
```http
POST /api/v1/s3/restart
```
Initiates a graceful backend restart.

### Configuration Storage
- Configuration is stored in the `backend/.env` file
- The system automatically updates environment variables
- Changes take effect after backend restart

### Security Considerations
- AWS credentials are stored in the backend environment
- Credentials are not exposed in API responses
- Use HTTPS in production to protect credential transmission
- Consider using IAM roles instead of access keys in production

## 🛠️ Manual Restart (Alternative)

If the automatic restart doesn't work, you can manually restart using the provided script:

```bash
cd backend
./restart.sh
```

Or manually:
```bash
cd backend
# Stop existing process
pkill -f "go run main.go"
# Start new process
go run main.go
```

## 🔍 Troubleshooting

### Configuration Not Applied
- Ensure you clicked "Restart Backend" after updating configuration
- Check that the backend restarted successfully
- Verify the `.env` file was updated correctly

### Backend Won't Restart
- Check backend logs for errors
- Ensure Go is installed and accessible
- Try manual restart using the script or commands above

### S3 Connection Issues
- Verify AWS credentials are correct and not expired
- Check that the S3 bucket exists and is accessible
- Ensure the AWS region is correct
- Verify network connectivity to AWS

### Permission Errors
- Ensure the backend has write permissions to the `.env` file
- Check that AWS credentials have necessary S3 permissions
- Verify bucket policies allow access from your credentials

## 📋 Environment Variables

The system manages these environment variables automatically:

```bash
ENABLE_S3_INTEGRATION=true
AWS_REGION=eu-central-1
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_SESSION_TOKEN=your_session_token
AWS_S3_BUCKET=your-bucket-name
AWS_S3_DATA_PREFIX=workflowtest/dev/ourl-lemon/
AWS_S3_VIDEO_PREFIX=workflowtest/dev/ourl-lemon/
S3_SYNC_INTERVAL_MINUTES=5
S3_SYNC_DAYS=3
```

## 🔄 Migration from Manual Configuration

If you previously configured S3 manually:
1. Your existing `.env` configuration will be preserved
2. The UI will load your current settings automatically
3. You can now manage everything through the web interface
4. No data loss or reconfiguration required

## 🎉 Next Steps

After setting up S3 configuration:
1. Test the connection using the "Trigger Sync" button
2. Monitor the S3 status dashboard
3. Check that test data is being ingested properly
4. Verify video attachments are accessible

For more detailed S3 integration information, see `S3_INTEGRATION.md`.
