#!/bin/bash

# S3 Configuration Backend Restart Script
# This script helps restart the backend after configuration changes

echo "🔄 Restarting Test Visualization Platform Backend..."

# Check if we're in the backend directory
if [ ! -f "main.go" ]; then
    echo "❌ Error: Please run this script from the backend directory"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  Warning: .env file not found. Creating from template..."
    if [ -f ".env.template" ]; then
        cp .env.template .env
        echo "✅ Created .env file from template"
    else
        echo "❌ Error: .env.template not found"
        exit 1
    fi
fi

# Kill existing backend process if running
echo "🔍 Checking for existing backend processes..."
BACKEND_PID=$(pgrep -f "go run main.go" || pgrep -f "./main")
if [ ! -z "$BACKEND_PID" ]; then
    echo "🛑 Stopping existing backend process (PID: $BACKEND_PID)..."
    kill -TERM $BACKEND_PID
    sleep 2
    
    # Force kill if still running
    if kill -0 $BACKEND_PID 2>/dev/null; then
        echo "🔨 Force stopping backend process..."
        kill -KILL $BACKEND_PID
    fi
    echo "✅ Backend process stopped"
else
    echo "ℹ️  No existing backend process found"
fi

# Wait a moment for cleanup
sleep 1

# Start the backend
echo "🚀 Starting backend..."
if command -v go &> /dev/null; then
    echo "📦 Using Go to run the backend..."
    nohup go run main.go > backend.log 2>&1 &
    BACKEND_PID=$!
    echo "✅ Backend started with PID: $BACKEND_PID"
    echo "📋 Backend logs are being written to backend.log"
else
    echo "❌ Error: Go is not installed or not in PATH"
    exit 1
fi

# Wait a moment and check if backend started successfully
sleep 3
if kill -0 $BACKEND_PID 2>/dev/null; then
    echo "🎉 Backend is running successfully!"
    echo "🌐 Backend should be available at http://localhost:8080"
    echo "📊 Check backend.log for detailed logs"
else
    echo "❌ Backend failed to start. Check backend.log for errors."
    exit 1
fi

echo "✨ Restart complete!"
